const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

// Create Express app
const app = express();
const port = process.env.PORT || 3003;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Log requests
app.use((req, _res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Request body:', JSON.stringify(req.body, null, 2));
  next();
});

// Import routes
const threadRoutes = require('./routes/threads');

// Use routes
app.use('/cc-lg/threads', threadRoutes);

// Start server
app.listen(port, () => {
  console.log(`Mock server running at http://localhost:${port}`);
  console.log(`API endpoint: http://localhost:${port}/cc-lg/threads`);
});

// Export for testing
module.exports = app;
