import { useState, useEffect, useRef } from 'react'
import EmojiPicker from 'emoji-picker-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

// Mapping of text emoticons to emoji characters
const emoticonMap = {
  ':)': '😊',
  ':-)': '😊',
  ':D': '😃',
  ':-D': '😃',
  ':(': '😞',
  ':-(': '😞',
  ';)': '😉',
  ';-)': '😉',
  ':p': '😛',
  ':P': '😛',
  ':-p': '😛',
  ':-P': '😛',
  ':o': '😮',
  ':O': '😮',
  '<3': '❤️',
  'XD': '😂',
  'xD': '😂',
  ':/': '😕',
  ':-/': '😕',
  ':"(': '😢',
  '8)': '😎',
  '8-)': '😎',
  '^_^': '😄'
}

// Chat Widget component
const ChatWidget = ({ apiUrl }) => {
  // Captcha state
  const [captchaPassed, setCaptchaPassed] = useState(false);
  const [captchaError, setCaptchaError] = useState(null);

  // Get initial values from localStorage if available
  const [isOpen, setIsOpen] = useState(() => {
    const savedIsOpen = localStorage.getItem('chatWidgetOpen')
    return savedIsOpen ? JSON.parse(savedIsOpen) : false
  })
  const [inputValue, setInputValue] = useState('')
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const emojiPickerRef = useRef(null)
  const [messages, setMessages] = useState(() => {
    const savedMessages = localStorage.getItem('chatWidgetMessages')
    return savedMessages ? JSON.parse(savedMessages) : []
  })
  // Get threadId from localStorage or let SDK manage it
  const [threadId, setThreadId] = useState(() => {
    const savedThreadId = localStorage.getItem('chatWidgetThreadId')
    return savedThreadId || null
  })
  // Dark mode state - get from localStorage or default to false
  const [darkMode, setDarkMode] = useState(() => {
    const savedDarkMode = localStorage.getItem('chatWidgetDarkMode')
    return savedDarkMode ? JSON.parse(savedDarkMode) : false
  })
  // State for resizable window width - get from localStorage or default to 320px
  const [windowWidth, setWindowWidth] = useState(() => {
    const savedWidth = localStorage.getItem('chatWidgetWidth')
    return savedWidth ? parseInt(savedWidth) : 320
  })
  const [isResizing, setIsResizing] = useState(false)
  const messagesEndRef = useRef(null)
  const chatContainerRef = useRef(null)

  // State for loading and error handling
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  // Base URL for API requests - use provided apiUrl or fallback to env variable
  const API_URL = apiUrl || import.meta.env.VITE_API_URL;

  // Start a new conversation
  const startNewConversation = () => {
    console.log("Starting new conversation...");

    // Clear all messages and thread data
    setMessages([]);
    setThreadId(null);
    localStorage.removeItem('chatWidgetThreadId');
    localStorage.removeItem('chatWidgetMessages');

    // Generate new thread ID
    createThread();

    // Add welcome message
    const welcomeMessage = [{
      id: 'welcome',
      text: 'Καλωσήρθατε! Είμαι ο έξυπνος προσωπικός σας βοηθός, πώς μπορώ να σας βοηθήσω;',
      isUser: false,
      timestamp: null
    }];
    setMessages(welcomeMessage);
    localStorage.setItem('chatWidgetMessages', JSON.stringify(welcomeMessage));
  }

  // Generate new thread ID
  const createThread = () => {
    const newThreadId = crypto.randomUUID();
    console.log('Created new thread ID:', newThreadId);
    setThreadId(newThreadId);
    localStorage.setItem('chatWidgetThreadId', newThreadId);
    return newThreadId;
  }

  // Clear all chat data (logout)
  const logout = () => {
    console.log("Logging out...");

    // Clear all chat-related data from localStorage
    localStorage.removeItem('chatWidgetThreadId');
    localStorage.removeItem('chatWidgetMessages');
    localStorage.removeItem('chatWidgetOpen');

    // Reset states
    setMessages([]);
    setThreadId(null);
    setIsOpen(false);

    // Add logout confirmation message
    const logoutMessage = [{
      id: 'logout',
      text: 'You have been logged out. Start a new conversation to continue.',
      isUser: false,
      timestamp: null
    }];
    setMessages(logoutMessage);
  }

  // Send message to thread with error handling
  const sendMessageToThread = async (threadId, message) => {
    console.log('Sending message:', { threadId, message });
    try {
      const response = await fetch(`${API_URL}/threads/${threadId}/runs/wait`, {
        method: 'POST',
        body: JSON.stringify({
          header: {
            ID: crypto.randomUUID(),
            Application: "A7459F80-5761-4E53-9270-B1FFA5C91D4F",
            Bank: "NBG",
            AgentId: ""
          },
          payload: {
            UserMessage: message
          }
        })
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.exception?.message || 'Failed to send message');
      }

      // Validate agent response
      if (!data.payload || !data.payload.agentMessage) {
        throw new Error('No response received from agent');
      }

      setError(null); // Clear any previous errors
      return data.payload;

    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error(error.message || 'Failed to send message');
    }
  }

  // Handle incoming agent message
  const handleAgentMessage = (agentMessage) => {
    console.log('Received agent message:', agentMessage);
    setMessages(prev => {
      const filteredMessages = prev.filter(msg => msg.text !== "Processing...")
      const newMessages = [
        ...filteredMessages,
        {
          id: Date.now(),
          text: agentMessage,
          isUser: false,
          timestamp: new Date().toISOString()
        }
      ]
      localStorage.setItem('chatWidgetMessages', JSON.stringify(newMessages))
      return newMessages
    })
  }

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages]);

  // Handle click outside emoji picker
  useEffect(() => {
    function handleClickOutside(event) {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target)) {
        setShowEmojiPicker(false)
      }
    }

    // Add event listener when emoji picker is shown
    if (showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    // Clean up the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showEmojiPicker]);

  const toggleChat = () => {
    const newIsOpen = !isOpen
    setIsOpen(newIsOpen)
    // Save to localStorage
    localStorage.setItem('chatWidgetOpen', JSON.stringify(newIsOpen))
  }

  const handleInputChange = (e) => {
    setInputValue(e.target.value)
  }

  // Handle emoji selection
  const handleEmojiClick = (emojiObject) => {
    setInputValue(prevInput => prevInput + emojiObject.emoji)
    setShowEmojiPicker(false) // Hide picker after selection
  }

  // Toggle emoji picker
  const toggleEmojiPicker = (e) => {
    e.preventDefault()
    setShowEmojiPicker(!showEmojiPicker)
  }

  // Toggle dark mode
  const toggleDarkMode = (e) => {
    e.preventDefault()
    const newDarkMode = !darkMode
    setDarkMode(newDarkMode)
    // Save to localStorage
    localStorage.setItem('chatWidgetDarkMode', JSON.stringify(newDarkMode))
  }

  // Format timestamp to a user-friendly format
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    const isYesterday = new Date(now - 86400000).toDateString() === date.toDateString();

    // Format: HH:MM for today, "Yesterday" for yesterday, MM/DD HH:MM for other days
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    if (isToday) {
      return `${hours}:${minutes}`;
    } else if (isYesterday) {
      return `Yesterday ${hours}:${minutes}`;
    } else {
      // Check if it's the current year
      const isCurrentYear = date.getFullYear() === now.getFullYear();
      if (isCurrentYear) {
        return `${month}/${day} ${hours}:${minutes}`;
      } else {
        // Include the year for dates from previous years
        return `${month}/${day}/${date.getFullYear().toString().substr(2)} ${hours}:${minutes}`;
      }
    }
  }

  // Get full timestamp format for tooltip/title
  const getFullTimestamp = (timestamp) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const options = {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    };

    return date.toLocaleString(undefined, options);
  }

  // Function to convert text emoticons to emoji characters
  const convertEmoticonsToEmojis = (text) => {
    let convertedText = text;

    // Create a regex pattern from the emoticon keys, escaping special characters
    const pattern = Object.keys(emoticonMap)
      .map(key => key.replace(/([.*+?^=!:${}()|[\]/\\])/g, '\\$1'))
      .join('|');

    // Replace emoticons with emojis
    if (pattern) {
      const regex = new RegExp(pattern, 'g');
      convertedText = text.replace(regex, match => emoticonMap[match] || match);
    }

    return convertedText;
  };

  const handleSendMessage = async (e) => {
    e.preventDefault()
    if (inputValue.trim() === '') return

    const convertedText = convertEmoticonsToEmojis(inputValue)
    setIsLoading(true)
    setError(null)

    try {
      // Add user message to chat
      setMessages(prev => {
        const newMessages = [
          ...prev,
          {
            id: Date.now(),
            text: convertedText,
            isUser: true,
            timestamp: new Date().toISOString()
          }
        ]
        localStorage.setItem('chatWidgetMessages', JSON.stringify(newMessages))
        return newMessages
      })

      // Add processing message
      setMessages(prev => {
        const newMessages = [
          ...prev,
          {
            id: Date.now() + 1,
            text: "Processing...",
            isUser: false,
            timestamp: null
          }
        ]
        localStorage.setItem('chatWidgetMessages', JSON.stringify(newMessages))
        return newMessages
      })

      // Get or create thread ID
      const currentThreadId = threadId || createThread()
      if (!currentThreadId) {
        throw new Error('Failed to generate thread ID')
      }

      // Send message to thread
      const response = await sendMessageToThread(currentThreadId, convertedText)

      // Handle agent response
      if (response && response.agentMessage) {
        handleAgentMessage(response.agentMessage)
      }

    } catch (error) {
      console.error('Error in handleSendMessage:', error)
      setError(error.message)
      setMessages(prev => {
        const newMessages = [
          ...prev,
          {
            id: Date.now(),
            text: `Error: ${error.message || 'Failed to send message'}`,
            isUser: false,
            timestamp: null
          }
        ]
        localStorage.setItem('chatWidgetMessages', JSON.stringify(newMessages))
        return newMessages
      })
    } finally {
      setIsLoading(false)
      setInputValue('')
    }
  }

  // Widget Styles - Tidio inspired design
  const widgetStyle = {
    position: 'fixed',
    bottom: '20px',
    right: '20px',
    zIndex: 9999,
    fontFamily: '"Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
  }

  // Modern chat button style inspired by NBG
  const chatButtonStyle = {
    width: isOpen ? '60px' : '60px',
    height: isOpen ? '60px' : '60px',
    borderRadius: '50%',
    background: isOpen
      ? (error ? '#dc3545' : '#0b2d7a')
      : 'linear-gradient(135deg, #0b2d7a 0%, #0a1f4d 100%)',
    color: 'white',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    boxShadow: '0 4px 15px rgba(11, 45, 122, 0.4)',
    fontSize: '24px',
    fontWeight: 'bold',
    transition: 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    zIndex: 9999,
    border: 'none',
    transform: isOpen ? 'scale(1) rotate(0deg)' : 'scale(1.05)',
    overflow: 'hidden'
  }

  // Modern chat window style
  const chatWindowStyle = {
    position: 'absolute',
    bottom: '80px',
    right: '0',
    width: `${windowWidth}px`,
    height: '650px',
    backgroundColor: darkMode ? '#1e1e2d' : 'white',
    borderRadius: '16px',
    boxShadow: darkMode ? '0 10px 25px rgba(0, 0, 0, 0.3)' : '0 10px 25px rgba(0, 0, 0, 0.1)',
    display: 'flex',
    flexDirection: 'column',
    zIndex: 9999,
    overflow: 'hidden',
    border: darkMode ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.08)',
    transition: isResizing ? 'none' : 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)'
  }

  // Modern header style - NBG inspired
  const chatHeaderStyle = {
    padding: '20px',
    background: 'linear-gradient(135deg, #0b2d7a 0%, #0a1f4d 100%)',
    color: 'white',
    borderTopLeftRadius: '16px',
    borderTopRightRadius: '16px',
    transition: 'background 0.3s ease',
    boxShadow: 'none', // Remove shadow for cleaner look
    display: 'flex',
    flexDirection: 'column',
    gap: '10px',
    marginBottom: 0,
    paddingBottom: '20px'
  }

  // Avatar style for header - perfect circle
  const avatarStyle = {
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    backgroundColor: 'white',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: '15px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
    color: '#0b2d7a',
    border: '2px solid rgba(255, 255, 255, 0.8)',
    overflow: 'hidden',
    padding: 0,
    minWidth: '40px',
    minHeight: '40px',
    maxWidth: '40px',
    maxHeight: '40px'
  }

  // Modern messages container
  const messagesContainerStyle = {
    flex: 1,
    padding: '25px 20px 20px 20px', // Extra padding at the top
    overflowY: 'auto',
    display: 'flex',
    flexDirection: 'column',
    gap: '15px',
    backgroundColor: darkMode ? '#2a2a3c' : '#f9fafc', // Dark/Light background for messages
    borderTop: 'none',
    marginTop: 0, // No margin needed with the new wave design
    position: 'relative', // Ensure proper positioning
    zIndex: 0 // Lower than the wave
  }

  // Modern message bubble style
  const messageStyle = (isUser) => ({
    maxWidth: '85%',
    padding: '12px 16px',
    borderRadius: isUser ? '18px 18px 0 18px' : '18px 18px 18px 0',
    backgroundColor: isUser
      ? '#0b2d7a'
      : darkMode ? '#3a3a4c' : 'white',
    color: isUser
      ? 'white'
      : darkMode ? '#e1e1e6' : '#333',
    alignSelf: isUser ? 'flex-end' : 'flex-start',
    wordBreak: 'break-word',
    boxShadow: isUser
      ? '0 2px 8px rgba(11, 45, 122, 0.25)'
      : darkMode ? '0 2px 8px rgba(0, 0, 0, 0.2)' : '0 2px 8px rgba(0, 0, 0, 0.08)',
    margin: '2px 0',
    position: 'relative',
    border: 'none',
    fontSize: '14px',
    lineHeight: '1.5',
    transition: 'all 0.2s ease',
    textAlign: 'left' // Ensure all text is left-aligned
  })

  // Markdown content styles
  const markdownStyles = {
    container: {
      width: '100%',
    },
    content: {
      h1: {
        fontSize: '1.2em',
        fontWeight: 'bold',
        margin: '0.5em 0',
        borderBottom: darkMode ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',
        paddingBottom: '0.2em'
      },
      h2: {
        fontSize: '1.1em',
        fontWeight: 'bold',
        margin: '0.4em 0'
      },
      h3: {
        fontSize: '1em',
        fontWeight: 'bold',
        margin: '0.3em 0'
      },
      a: {
        color: darkMode ? '#8ab4f8' : '#0b2d7a',
        textDecoration: 'underline'
      },
      userLink: {
        color: '#ffffff',
        textDecoration: 'underline'
      },
      code: {
        backgroundColor: darkMode ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)',
        padding: '0.2em 0.4em',
        borderRadius: '3px',
        fontFamily: 'monospace',
        fontSize: '0.9em'
      },
      userCode: {
        backgroundColor: 'rgba(255,255,255,0.2)',
        padding: '0.2em 0.4em',
        borderRadius: '3px',
        fontFamily: 'monospace',
        fontSize: '0.9em',
        color: 'white'
      },
      pre: {
        backgroundColor: darkMode ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)',
        padding: '0.5em',
        borderRadius: '5px',
        overflowX: 'auto',
        fontFamily: 'monospace',
        fontSize: '0.9em',
        margin: '0.5em 0'
      },
      ul: {
        paddingLeft: '1.5em',
        margin: '0.3em 0'
      },
      ol: {
        paddingLeft: '1.5em',
        margin: '0.3em 0'
      },
      li: {
        margin: '0.2em 0'
      },
      blockquote: {
        borderLeft: darkMode ? '3px solid rgba(255,255,255,0.2)' : '3px solid rgba(0,0,0,0.2)',
        paddingLeft: '0.5em',
        margin: '0.5em 0',
        fontStyle: 'italic'
      },
      table: {
        borderCollapse: 'collapse',
        width: '100%',
        margin: '0.5em 0',
        fontSize: '0.9em'
      },
      th: {
        border: darkMode ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',
        padding: '0.3em',
        backgroundColor: darkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
      },
      td: {
        border: darkMode ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',
        padding: '0.3em'
      },
      hr: {
        border: 0,
        height: '1px',
        backgroundColor: darkMode ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
        margin: '0.5em 0'
      },
      p: {
        margin: '0.3em 0'
      }
    }
  }

  // Modern input container
  const inputContainerStyle = {
    display: 'flex',
    padding: '15px 20px',
    borderTop: darkMode ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.05)',
    backgroundColor: darkMode ? '#1e1e2d' : 'white',
    alignItems: 'center'
  }

  // Modern input style
  const inputStyle = {
    flex: 1,
    padding: '12px 18px',
    paddingLeft: '40px', // Make room for emoji button on the left
    paddingRight: '16px', // Reduced padding on right so text doesn't go under button
    borderRadius: '24px',
    border: darkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)',
    outline: 'none',
    fontSize: '14px',
    boxShadow: 'none',
    transition: 'all 0.2s ease',
    backgroundColor: darkMode ? '#3a3a4c' : '#f5f7fa',
    color: darkMode ? '#e1e1e6' : '#000000', // Adjust text color for dark mode
    fontWeight: '400', // Regular weight for better readability
    width: 'calc(100% - 50px)', // Leave space for the send button
    textAlign: 'left', // Explicitly set text alignment to left
    marginRight: '50px' // Add margin to prevent overlap with send button
  }

  // Modern send button
  const sendButtonStyle = {
    marginLeft: '10px',
    width: '40px',
    height: '40px',
    backgroundColor: isLoading ? 'rgba(11, 45, 122, 0.5)' : '#0b2d7a',
    color: 'white',
    border: 'none',
    borderRadius: '50%',
    cursor: isLoading ? 'not-allowed' : 'pointer',
    opacity: isLoading ? 0.7 : 1,
    boxShadow: '0 2px 8px rgba(11, 45, 122, 0.3)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontWeight: 'bold',
    transition: 'all 0.2s ease',
    fontSize: '18px'
  }

  // Modern stop button
  const stopButtonStyle = {
    marginLeft: '10px',
    width: '40px',
    height: '40px',
    backgroundColor: '#dc3545',
    color: 'white',
    border: 'none',
    borderRadius: '50%',
    cursor: 'pointer',
    boxShadow: '0 2px 8px rgba(220, 53, 69, 0.3)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontWeight: 'bold',
    transition: 'all 0.2s ease',
    fontSize: '18px'
  }

  // Status indicator style
  const statusIndicatorStyle = {
    width: '10px',
    height: '10px',
    borderRadius: '50%',
    backgroundColor: error ? '#dc3545' : (isLoading ? '#ffc107' : '#28a745'),
    marginLeft: '8px',
    boxShadow: '0 0 0 2px white'
  }

  // Add placeholder welcome message if no messages exist
  const displayMessages = messages.length > 0
    ? messages
    : [{
        id: 'welcome-' + Date.now(),
        text: 'Καλωσήρθατε! Είμαι ο έξυπνος προσωπικός σας βοηθός, πώς μπορώ να σας βοηθήσω;',
        isUser: false,
        timestamp: null // No timestamp for welcome message
      }];

  // Handle resize start
  const handleResizeStart = (e) => {
    e.preventDefault()
    e.stopPropagation() // Prevent event bubbling

    setIsResizing(true)

    // Add a class to the body to change cursor during resize
    document.body.classList.add('resizing')

    // Add event listeners for mouse/touch move and end
    document.addEventListener('mousemove', handleResizeMove)
    document.addEventListener('mouseup', handleResizeEnd)
    document.addEventListener('touchmove', handleResizeMove, { passive: false })
    document.addEventListener('touchend', handleResizeEnd)
    document.addEventListener('touchcancel', handleResizeEnd)
  }

  // Handle resize during mouse move
  const handleResizeMove = (e) => {
    if (!isResizing || !chatContainerRef.current) return

    // Prevent default behavior to avoid scrolling on mobile
    if (e.cancelable) e.preventDefault()

    // Get the client X position from either mouse or touch event
    const clientX = e.touches && e.touches[0] ? e.touches[0].clientX : e.clientX

    // Calculate new width based on mouse/touch position
    // We're resizing from the left side, so we need to calculate from the right edge
    const chatWidgetRect = chatContainerRef.current.getBoundingClientRect()
    const newWidth = Math.round(chatWidgetRect.right - clientX)

    // Set minimum and maximum width constraints
    const minWidth = 280
    const maxWidth = 600

    // Check if the new width would cause the left edge to go off-screen
    const windowWidth = window.innerWidth
    const rightEdgePosition = chatWidgetRect.right
    const leftEdgePosition = rightEdgePosition - newWidth

    // Ensure the left edge doesn't go off-screen (with a 20px margin)
    if (leftEdgePosition < 20) {
      return
    }

    // Apply the new width if it's within constraints
    if (newWidth >= minWidth && newWidth <= maxWidth) {
      // Apply the width directly to the element for immediate feedback
      chatContainerRef.current.style.width = `${newWidth}px`
      // Update state
      setWindowWidth(newWidth)
    }
  }

  // Handle resize end
  const handleResizeEnd = () => {
    if (!isResizing) return

    setIsResizing(false)

    // Remove the resizing class from body
    document.body.classList.remove('resizing')

    // Save the new width to localStorage
    localStorage.setItem('chatWidgetWidth', windowWidth.toString())

    // Remove event listeners
    document.removeEventListener('mousemove', handleResizeMove)
    document.removeEventListener('mouseup', handleResizeEnd)
    document.removeEventListener('touchmove', handleResizeMove)
    document.removeEventListener('touchend', handleResizeEnd)
    document.removeEventListener('touchcancel', handleResizeEnd)
  }

  // Resize handle style
  const resizeHandleStyle = {
    position: 'absolute',
    left: '0',
    top: '0',
    width: '12px', // Wider to make it easier to grab
    height: '100%',
    cursor: 'ew-resize',
    zIndex: 10000,
    backgroundColor: darkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    touchAction: 'none' // Prevent default touch actions for better mobile support
  }

 // Mount Google reCAPTCHA widget when overlay is shown.
 useEffect(() => {
   if (!isOpen || captchaPassed) return;
   let intervalId;
   function tryRenderRecaptcha() {
     if (
       window.grecaptcha &&
       typeof window.grecaptcha.render === 'function' &&
       document.getElementById('recaptcha-widget') &&
       !document.getElementById('recaptcha-widget').hasChildNodes()
     ) {
       window.grecaptcha.render('recaptcha-widget', {
         sitekey: '6LfDVEMrAAAAAECe1o_VW-hjz0ipeEp2h-BbhsGV',
         callback: async (token) => {
          setCaptchaPassed(true)
          //  try {
          //    setCaptchaError(null);
          //    const res = await fetch('/api/verify-captcha', {
          //      method: 'POST',
          //      headers: { 'Content-Type': 'application/json' },
          //      body: JSON.stringify({ token })
          //    });
          //    const data = await res.json();
          //    if (data.success) {
          //      setCaptchaPassed(true);
          //    } else {
          //      setCaptchaError('Captcha verification failed. Please try again.');
          //      window.grecaptcha.reset();
          //    }
          //  } catch (e) {
          //    setCaptchaError('Captcha verification error.');
          //    window.grecaptcha.reset();
          //  }
         },
         'expired-callback': () => {
           setCaptchaError('Captcha expired. Please try again.');
         }
       });
       clearInterval(intervalId);
     }
   }
   intervalId = setInterval(tryRenderRecaptcha, 200);
   // Clean up: remove widget if component unmounts or closes
   return () => {
     clearInterval(intervalId);
     const el = document.getElementById('recaptcha-widget');
     if (el) el.innerHTML = '';
   };
 }, [isOpen, captchaPassed]);

  return (
    <div style={widgetStyle}>
      {isOpen && (
        <div ref={chatContainerRef} className="chat-widget-container" style={chatWindowStyle}>
          {/* CAPTCHA OVERLAY */}
          {!captchaPassed && (
            <div
              style={{
                position: 'absolute',
                zIndex: 10001,
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: 'rgba(255,255,255,0.97)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <div style={{ marginBottom: 24, fontWeight: 600, fontSize: 18, color: '#0b2d7a' }}>
                Please complete the captcha to access the chat
              </div>
              <div id="recaptcha-widget"></div>
              {captchaError && (
                <div style={{ color: 'red', marginTop: 12 }}>{captchaError}</div>
              )}
            </div>
          )}
          {/* Resize handle */}
          <div
            style={resizeHandleStyle}
            onMouseDown={handleResizeStart}
            onTouchStart={handleResizeStart}
            title="Drag to resize"
            className="resize-handle"
          >
            {/* Three dots to indicate draggable area */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
              <div style={{
                width: '4px',
                height: '4px',
                backgroundColor: darkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.2)',
                borderRadius: '50%',
              }} />
              <div style={{
                width: '4px',
                height: '4px',
                backgroundColor: darkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.2)',
                borderRadius: '50%',
              }} />
              <div style={{
                width: '4px',
                height: '4px',
                backgroundColor: darkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.2)',
                borderRadius: '50%',
              }} />
            </div>
          </div>
          <div style={chatHeaderStyle}>
            <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between'}}>
              <div style={{display: 'flex', alignItems: 'center'}}>
                <div style={avatarStyle}>
                  <i className="fas fa-robot" style={{ fontSize: '18px' }}></i>
                </div>
                <div>
                  <h3 style={{margin: 0, fontSize: '18px', fontWeight: '600'}}>AI Assistant</h3>
                  <p style={{margin: '4px 0 0', fontSize: '13px', opacity: 0.9}}>
                    {isLoading ? (
                      <span className="typing-animation">Typing<span className="dot">.</span><span className="dot">.</span><span className="dot">.</span></span>
                    ) : 'Online'}
                    <span style={{display: 'inline-block', marginLeft: '5px', width: '8px', height: '8px', borderRadius: '50%', backgroundColor: isLoading ? '#ffc107' : '#28a745'}}></span>
                  </p>
                </div>
              </div>
              <div style={{display: 'flex', alignItems: 'center'}}>
                {/* New Chat button - icon only */}
                <button
                  onClick={startNewConversation}
                  style={{
                    marginRight: '10px',
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    width: '32px',
                    height: '32px',
                    fontSize: '14px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s ease'
                  }}
                  title="Start New Conversation"
                >
                  <i className="fas fa-sync-alt"></i>
                </button>
                {/* Logout button - icon only */}
                <button
                  onClick={logout}
                  style={{
                    marginRight: '10px',
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    width: '32px',
                    height: '32px',
                    fontSize: '14px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s ease'
                  }}
                  title="Logout"
                >
                  <i className="fas fa-sign-out-alt"></i>
                </button>
                {/* Dark mode toggle button - icon only */}
                <button
                  onClick={toggleDarkMode}
                  style={{
                    marginRight: '10px',
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    width: '32px',
                    height: '32px',
                    fontSize: '14px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s ease'
                  }}
                  title={darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
                >
                  <i className={`fas fa-${darkMode ? 'sun' : 'moon'}`}></i>
                </button>
                <div
                  style={{
                    width: '12px',
                    height: '12px',
                    borderRadius: '50%',
                    backgroundColor: error ? '#dc3545' : (isLoading ? '#ffc107' : '#28a745'),
                    boxShadow: '0 0 0 2px rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.3s ease'
                  }}
                  title={error ? 'Error' : (isLoading ? 'Processing' : 'Connected')}
                />
              </div>
            </div>
            <p style={{margin: '5px 0 0', fontSize: '12px', opacity: 0.8}}>
              {error
                ? 'Connection error: ' + error
                : (isLoading
                   ? 'Processing...'
                   : (threadId
                      ? `Connected`
                      : 'Ready to chat'))}
            </p>
          </div>
          {/* Exact Tidio-style wave decoration */}
          <div style={{
            position: 'relative',
            height: '30px', // Increased height for more prominent wave
            background: 'linear-gradient(135deg, #0b2d7a 0%, #0a1f4d 100%)', // Match header gradient exactly
            marginTop: '-1px',
            overflow: 'hidden',
            zIndex: 1 // Ensure proper stacking
          }}>
            <svg
              style={{
                position: 'absolute',
                bottom: '-1px',
                left: 0,
                width: '100%',
                height: '31px', // Increased to match container height
                display: 'block'
              }}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 1000 100"
              preserveAspectRatio="none"
            >
              <path
                d="M0,0 C200,40 350,40 500,20 C650,0 800,10 1000,30 L1000,100 L0,100 Z"
                fill={darkMode ? '#2a2a3c' : '#f9fafc'}
              />
            </svg>
          </div>
          <div style={messagesContainerStyle}>
            {/* Messages */}
            {displayMessages.map((message, index) => (
              <div key={`${message.id}-${index}`} style={{ display: 'flex', flexDirection: 'column', alignItems: message.isUser ? 'flex-end' : 'flex-start', marginBottom: '16px' }}>
                {/* Message row with icon and bubble */}
                <div style={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: '8px',
                  width: '100%',
                  justifyContent: message.isUser ? 'flex-end' : 'flex-start' // Align user messages to the right
                }}>
                  {/* User or AI icon */}
                  {!message.isUser && (
                    <div style={{
                      minWidth: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '16px',
                      color: '#0b2d7a',
                      fontWeight: 'bold',
                      padding: '2px',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                      border: '1px solid rgba(11, 45, 122, 0.2)'
                    }}>
                      <i className="fas fa-robot"></i>
                    </div>
                  )}
                  {/* Message bubble */}
                  <div style={{
                    ...messageStyle(message.isUser),
                    maxWidth: '85%', // Consistent max width for all messages
                  }}>
                    <div className="markdown-content">
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm]}
                        components={{
                        h1: ({node, ...props}) => <h1 style={markdownStyles.content.h1} {...props} />,
                        h2: ({node, ...props}) => <h2 style={markdownStyles.content.h2} {...props} />,
                        h3: ({node, ...props}) => <h3 style={markdownStyles.content.h3} {...props} />,
                        a: ({node, ...props}) => <a style={message.isUser ? markdownStyles.content.userLink : markdownStyles.content.a} {...props} target="_blank" rel="noopener noreferrer" />,
                        code: ({node, inline, ...props}) =>
                          inline ? <code style={message.isUser ? markdownStyles.content.userCode : markdownStyles.content.code} {...props} /> :
                          <pre style={markdownStyles.content.pre}><code style={message.isUser ? {color: 'white'} : {}} {...props} /></pre>,
                        pre: ({node, ...props}) => <pre style={markdownStyles.content.pre} {...props} />,
                        ul: ({node, ...props}) => <ul style={markdownStyles.content.ul} {...props} />,
                        ol: ({node, ...props}) => <ol style={markdownStyles.content.ol} {...props} />,
                        li: ({node, ...props}) => <li style={markdownStyles.content.li} {...props} />,
                        blockquote: ({node, ...props}) => <blockquote style={markdownStyles.content.blockquote} {...props} />,
                        table: ({node, ...props}) => <table style={markdownStyles.content.table} {...props} />,
                        th: ({node, ...props}) => <th style={markdownStyles.content.th} {...props} />,
                        td: ({node, ...props}) => <td style={markdownStyles.content.td} {...props} />,
                        hr: ({node, ...props}) => <hr style={markdownStyles.content.hr} {...props} />,
                        p: ({node, ...props}) => <p style={markdownStyles.content.p} {...props} />
                      }}
                    >
                      {message.text}
                    </ReactMarkdown>
                    </div>
                  </div>
                </div>

                {/* Only show timestamp if it's available and valid */}
                {message.timestamp && !isNaN(new Date(message.timestamp).getTime()) && (
                  <div style={{
                    display: 'flex',
                    width: '100%',
                    justifyContent: message.isUser ? 'flex-end' : 'flex-start',
                    marginTop: '4px'
                  }}>
                    {/* Timestamp text with hover tooltip */}
                    <div
                      title={getFullTimestamp(message.timestamp)}
                      className="timestamp-display"
                      style={{
                        fontSize: '10px',
                        color: darkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
                        maxWidth: '85%', // Match message bubble width
                        textAlign: message.isUser ? 'right' : 'left',
                        paddingLeft: !message.isUser ? '40px' : '0', // Add padding for AI messages to align with bubble
                        paddingRight: message.isUser ? '8px' : '0', // Add padding for user messages
                        fontFamily: 'monospace', // Use monospace for consistent time display
                        letterSpacing: '0.5px', // Better readability for small text
                        borderRadius: '8px',
                        padding: '1px 4px',
                        transition: 'all 0.2s ease',
                        display: 'inline-block',
                        cursor: 'default'
                      }}>
                      {formatTimestamp(message.timestamp)}
                    </div>
                  </div>
                )}
              </div>
            ))}

            <div ref={messagesEndRef}/>
          </div>
          <form onSubmit={handleSendMessage} style={{...inputContainerStyle, padding: '12px'}}>
            <div style={{ position: 'relative', width: '100%', height: '44px' }}>
              {/* Emoji button positioned on the left */}
              <button
                type="button"
                onClick={toggleEmojiPicker}
                style={{
                  position: 'absolute',
                  left: '15px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  background: 'transparent',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '18px',
                  color: darkMode ? '#aaa' : '#888',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '0',
                  zIndex: 2,
                  pointerEvents: isLoading ? 'none' : 'auto',
                  opacity: isLoading ? 0.5 : 1
                }}
                disabled={isLoading}
              >
                <i className="fas fa-smile"></i>
              </button>

              {/* Input field - with reduced width to make room for send button */}
              <input
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                placeholder="Type your message..."
                style={{
                  ...inputStyle,
                  width: 'calc(100% - 55px)', // Adjust width for proper alignment
                  paddingLeft: '40px', // Space for emoji button
                  paddingRight: '10px',
                  marginRight: '0', // Remove margin
                  position: 'absolute', // Position absolutely
                  left: '0', // Align to the left
                  top: '50%', // Center vertically
                  transform: 'translateY(-50%)' // Perfect vertical centering
                }}
                disabled={isLoading}
                className="chat-input"
              />

              {/* Send or stop button - absolutely positioned on the right */}
              {isLoading ? (
                <button
                  type="button"
                  onClick={() => setIsLoading(false)}
                  style={{
                    position: 'absolute',
                    right: '0', // Keep button at the far right
                    top: '50%',
                    transform: 'translateY(-50%)',
                    ...stopButtonStyle,
                    width: '40px',
                    height: '40px'
                  }}
                >
                  <i className="fas fa-times"></i>
                </button>
              ) : (
                <button
                  type="submit"
                  style={{
                    position: 'absolute',
                    right: '0', // Keep button at the far right
                    top: '50%',
                    transform: 'translateY(-50%)',
                    ...sendButtonStyle,
                    width: '40px',
                    height: '40px'
                  }}
                  disabled={isLoading}
                >
                  <i className="fas fa-paper-plane"></i>
                </button>
              )}

              {/* Emoji picker */}
              {showEmojiPicker && (
                <div
                  ref={emojiPickerRef}
                  style={{
                    position: 'absolute',
                    bottom: '50px',
                    left: '0',
                    scale: '80%',
                    zIndex: 10
                  }}
                >
                  <EmojiPicker
                    onEmojiClick={handleEmojiClick}
                    width={300}
                    height={400}
                    previewConfig={{ showPreview: false }}
                    searchDisabled
                  />
                </div>
              )}
            </div>

            {/* Add a style tag for input focus state */}
            <style>{`
              .chat-input:focus {
                border-color: #0b2d7a;
                box-shadow: 0 0 0 2px rgba(11, 45, 122, 0.2);
                background-color: ${darkMode ? '#4a4a5c' : '#ffffff'};
              }
              .chat-input::placeholder {
                color: ${darkMode ? '#aaa' : '#888'};
                opacity: 0.8;
              }
              .resize-handle:hover {
                background-color: ${darkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.08)'};
              }
              .resize-handle:hover > div > div {
                background-color: ${darkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.4)'};
              }
              .resize-handle:active {
                background-color: ${darkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.12)'};
              }
              .resize-handle:active > div > div {
                background-color: ${darkMode ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.6)'};
              }
              body.resizing {
                cursor: ew-resize !important;
                user-select: none;
              }
              /* Timestamp hover effect */
              .timestamp-display:hover {
                background-color: ${darkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.1)'};
                color: ${darkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'};
              }
              /* Markdown styles */
              a {
                color: ${darkMode ? '#8ab4f8' : '#0b2d7a'};
                text-decoration: underline;
              }
              code {
                background-color: ${darkMode ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)'};
                padding: 0.2em 0.4em;
                border-radius: 3px;
                font-family: monospace;
                font-size: 0.9em;
              }
            `}</style>
          </form>
        </div>
      )}
      {/* Message count badge */}
      {!isOpen && messages.filter(msg => !msg.isUser).length > 0 && (
          <div style={{
            position: 'absolute',
            top: '-6px',
            right: '-6px',
            backgroundColor: '#ff3860',
            color: 'white',
            borderRadius: '50%',
            minWidth: '20px',
            height: '20px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            fontSize: '11px',
            fontWeight: 'bold',
            boxShadow: '0 2px 4px rgba(255, 56, 96, 0.4)',
            border: '2px solid white',
            zIndex: 1100,
            animation: 'pulse 2s infinite',
            padding: '1px 4px'
          }}>
            <style>{`
              @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
              }
              @keyframes fadeIn {
                0% { opacity: 0; }
                100% { opacity: 1; }
              }
              @keyframes blink {
                0% { opacity: 0.3; }
                50% { opacity: 1; }
                100% { opacity: 0.3; }
              }
              .fa-robot, .fa-comment, .fa-paper-plane, .fa-times, .fa-sync-alt, .fa-bell {
                animation: fadeIn 0.3s ease-in-out;
              }
              .fa-paper-plane:hover, .fa-sync-alt:hover {
                transform: scale(1.1);
                transition: transform 0.2s ease;
              }
              .typing-animation .dot {
                animation: blink 1.4s infinite;
                display: inline-block;
              }
              .typing-animation .dot:nth-child(2) {
                animation-delay: 0.2s;
              }
              .typing-animation .dot:nth-child(3) {
                animation-delay: 0.4s;
              }
            `}</style>
            <i className="fa fa-bell" style={{ fontSize: '10px', marginRight: '3px' }}></i>
            <span>{messages.filter(msg => !msg.isUser).length}</span>
          </div>
      )}
      <div onClick={toggleChat} style={chatButtonStyle}>
        {isOpen ? (
          <span style={{display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
            <i className="fas fa-times" style={{fontSize: '20px'}}></i>
          </span>
        ) : (
          <span style={{display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
            <i className="fas fa-comment" style={{fontSize: '24px'}}></i>
          </span>
        )}
      </div>
    </div>
  )
}

export default ChatWidget