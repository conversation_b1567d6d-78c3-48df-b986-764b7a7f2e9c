import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import theme from '../styles/theme';
import { formatTimestamp, getFullTimestamp } from '../utils/messageUtils';

/**
 * NBG Message bubble component
 * @param {Object} props - Component props
 * @param {Object} props.message - Message object
 * @param {boolean} props.darkMode - Whether dark mode is enabled
 * @param {string} props.position - Bubble position: 'top', 'middle', 'bottom', 'single'
 * @returns {JSX.Element} - Rendered component
 */
const MessageBubble = ({ message, darkMode, position = 'single' }) => {
  const { isUser, text, timestamp } = message;

  // NBG Profile Icon Component
  const NBGProfileIcon = ({ size = 'small' }) => {
    const iconSize = size === 'large' ? 40 : 24;
    const strokeWidth = size === 'large' ? 0.8 : 0.48;

    return (
      <div style={{
        width: `${iconSize}px`,
        height: `${iconSize}px`,
        borderRadius: theme.borderRadius.circle,
        backgroundColor: theme.colors.nbg.profileBg,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexShrink: 0,
        position: 'relative'
      }}>
        {/* Eva Avatar - simplified representation */}
        <div style={{
          width: `${iconSize * 0.6}px`,
          height: `${iconSize * 0.6}px`,
          borderRadius: theme.borderRadius.circle,
          backgroundColor: theme.colors.primary.main,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: theme.colors.text.light,
          fontSize: `${iconSize * 0.3}px`,
          fontWeight: theme.typography.fontWeight.bold
        }}>
          E
        </div>
      </div>
    );
  };

  // Get border radius based on position for bot messages
  const getBorderRadius = () => {
    if (isUser) {
      return theme.borderRadius.messageBubble.user;
    }

    switch (position) {
      case 'top':
        return theme.borderRadius.messageBubble.agent;
      case 'middle':
        return theme.borderRadius.messageBubble.agentMiddle;
      case 'bottom':
        return theme.borderRadius.messageBubble.agentBottom;
      default:
        return theme.borderRadius.messageBubble.agent;
    }
  };

  // NBG Message bubble style
  const messageStyle = {
    maxWidth: isUser ? '80%' : 'auto',
    padding: '8px 16px',
    borderRadius: getBorderRadius(),
    backgroundColor: isUser
      ? theme.colors.secondary.light  // White for user suggestions
      : theme.colors.secondary.light, // White for bot messages
    color: theme.colors.text.primary, // NBG primary text color
    border: 'none', // No border for any messages
    wordBreak: 'break-word',
    overflowWrap: 'break-word', // Ensure long words break
    boxShadow: 'none', // Remove shadows as per NBG design
    margin: '0',
    position: 'relative',
    fontSize: theme.typography.fontSize.md,
    lineHeight: theme.typography.lineHeight.normal,
    transition: theme.transitions.fast,
    textAlign: 'left',
    fontFamily: theme.typography.fontFamily,
    fontWeight: isUser ? theme.typography.fontWeight.bold : theme.typography.fontWeight.regular,
    width: '100%', // Ensure full width within container
    boxSizing: 'border-box' // Include padding in width calculation
  };

  // Markdown styles
  const markdownStyles = {
    container: {
      width: '100%',
    },
    content: {
      h1: {
        fontSize: '1.2em',
        fontWeight: 'bold',
        margin: '0.5em 0',
        borderBottom: darkMode ? '1px solid rgba(255,255,255,0.1)' : '1px solid rgba(0,0,0,0.1)',
        paddingBottom: '0.2em'
      },
      h2: {
        fontSize: '1.1em',
        fontWeight: 'bold',
        margin: '0.4em 0'
      },
      h3: {
        fontSize: '1em',
        fontWeight: 'bold',
        margin: '0.3em 0'
      },
      a: {
        color: darkMode ? '#8ab4f8' : theme.colors.primary.main,
        textDecoration: 'underline'
      },
      userLink: {
        color: theme.colors.text.light,
        textDecoration: 'underline'
      },
      code: {
        backgroundColor: darkMode ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)',
        padding: '0.2em 0.4em',
        borderRadius: '3px',
        fontFamily: 'monospace',
        fontSize: '0.9em'
      },
      userCode: {
        backgroundColor: 'rgba(255,255,255,0.2)',
        padding: '0.2em 0.4em',
        borderRadius: '3px',
        fontFamily: 'monospace',
        fontSize: '0.9em',
        color: 'white'
      },
      pre: {
        backgroundColor: darkMode ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)',
        padding: '0.5em',
        borderRadius: '5px',
        overflowX: 'auto',
        fontFamily: 'monospace',
        fontSize: '0.9em',
        margin: '0.5em 0'
      },
      ul: {
        paddingLeft: '1.5em',
        margin: '0.3em 0'
      },
      ol: {
        paddingLeft: '1.5em',
        margin: '0.3em 0'
      },
      li: {
        margin: '0.2em 0'
      },
      blockquote: {
        borderLeft: darkMode ? '3px solid rgba(255,255,255,0.2)' : '3px solid rgba(0,0,0,0.2)',
        paddingLeft: '0.5em',
        margin: '0.5em 0',
        fontStyle: 'italic'
      },
      table: {
        borderCollapse: 'collapse',
        width: '100%',
        margin: '0.5em 0',
        fontSize: '0.9em'
      },
      th: {
        border: darkMode ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',
        padding: '0.3em',
        backgroundColor: darkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
      },
      td: {
        border: darkMode ? '1px solid rgba(255,255,255,0.2)' : '1px solid rgba(0,0,0,0.2)',
        padding: '0.3em'
      },
      hr: {
        border: 0,
        height: '1px',
        backgroundColor: darkMode ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
        margin: '0.5em 0'
      },
      p: {
        margin: '0.3em 0'
      }
    }
  };

  // Timestamp style
  const timestampStyle = {
    fontSize: theme.typography.fontSize.xs,
    color: darkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
    maxWidth: '85%',
    textAlign: isUser ? 'right' : 'left',
    paddingLeft: !isUser ? '40px' : '0',
    paddingRight: isUser ? '8px' : '0',
    fontFamily: 'monospace',
    letterSpacing: '0.5px',
    borderRadius: theme.borderRadius.small,
    padding: '1px 4px',
    transition: theme.transitions.fast,
    display: 'inline-block',
    cursor: 'default'
  };

  // NBG layout: Bot messages show profile icon, user messages are suggestions without icon
  if (isUser) {
    // User suggestion button layout
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-end',
        marginBottom: '4px',
        width: '100%'
      }}>
        <div style={{
          ...messageStyle,
          cursor: 'pointer',
          transition: theme.transitions.fast
        }}
        onMouseEnter={(e) => {
          e.target.style.boxShadow = theme.shadows.small;
        }}
        onMouseLeave={(e) => {
          e.target.style.boxShadow = 'none';
        }}>
          <span>{text}</span>
        </div>
      </div>
    );
  }

  // Bot message layout with profile icon
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      marginBottom: '4px',
      width: '100%'
    }}>
      {/* Message row with icon and bubble */}
      <div style={{
        display: 'flex',
        alignItems: 'flex-end',
        gap: '8px',
        width: '100%'
      }}>
        {/* NBG Profile icon */}
        <NBGProfileIcon size="small" />

        {/* Message bubble */}
        <div style={messageStyle}>
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
              p: ({node, ...props}) => <p style={{margin: 0}} {...props} />
            }}
          >
            {text}
          </ReactMarkdown>
        </div>
      </div>

      {/* Timestamp (only show if available and valid) */}
      {timestamp && !isNaN(new Date(timestamp).getTime()) && (
        <div style={{
          display: 'flex',
          width: '100%',
          justifyContent: 'flex-start',
          marginTop: '4px',
          paddingLeft: '32px' // Align with message bubble
        }}>
          <div
            title={getFullTimestamp(timestamp)}
            className="timestamp-display"
            style={timestampStyle}
          >
            {formatTimestamp(timestamp)}
          </div>
        </div>
      )}
    </div>
  );
};

export default MessageBubble;
