* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Aeonik Pro', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.chat-container {
    width: 408px;
    height: 600px;
    background: white;
    border-radius: 12px;
    box-shadow: 0px 6px 15px 5px rgba(0, 56, 65, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header Styles */
.chat-header {
    background: linear-gradient(135deg, #1a5f5f 0%, #2d7a7a 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-container {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.chat-title {
    font-size: 18px;
    font-weight: 600;
}

.header-right {
    display: flex;
    gap: 8px;
}

.minimize-btn, .close-btn {
    background: none;
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.minimize-btn:hover, .close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Messages Area */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.message {
    display: flex;
    flex-direction: column;
}

.bot-message {
    align-items: flex-start;
}

.user-message {
    align-items: flex-end;
}

.message-content {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
}

.bot-message .message-content {
    background: #1a5f5f;
    color: white;
    border-bottom-left-radius: 4px;
}

.user-message .message-content {
    background: white;
    color: #333;
    border: 1px solid #e1e5e9;
    border-bottom-right-radius: 4px;
}

/* Quick Action Card */
.quick-action-card {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 16px;
    margin-top: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.quick-action-card:hover {
    border-color: #1a5f5f;
    box-shadow: 0 2px 8px rgba(26, 95, 95, 0.1);
}

.quick-action-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.quick-action-title {
    font-weight: 600;
    color: #1a5f5f;
    font-size: 14px;
}

.quick-action-description {
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
}

.quick-action-btn {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
    color: #333;
}

.quick-action-btn:hover {
    border-color: #1a5f5f;
    background: #f8f9fa;
}

.quick-action-btn i {
    color: #1a5f5f;
    font-size: 12px;
}

/* Input Area */
.chat-input-container {
    padding: 16px 20px;
    background: white;
    border-top: 1px solid #e1e5e9;
}

.input-wrapper {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 24px;
    padding: 4px;
    border: 1px solid #e1e5e9;
    transition: border-color 0.2s;
}

.input-wrapper:focus-within {
    border-color: #1a5f5f;
}

.chat-input {
    flex: 1;
    border: none;
    background: none;
    padding: 12px 16px;
    font-size: 14px;
    outline: none;
    color: #333;
}

.chat-input::placeholder {
    color: #999;
}

.attachment-btn {
    background: none;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #666;
    transition: all 0.2s;
}

.attachment-btn:hover {
    background: #e1e5e9;
    color: #1a5f5f;
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
    width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #999;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message {
    animation: fadeInUp 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 480px) {
    .chat-container {
        width: 100%;
        height: 100vh;
        border-radius: 0;
    }

    .message-content {
        max-width: 90%;
    }
}

/* Focus States */
.quick-action-btn:focus,
.quick-action-card:focus {
    outline: 2px solid #1a5f5f;
    outline-offset: 2px;
}

.minimize-btn:focus,
.close-btn:focus,
.attachment-btn:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* Loading State */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        opacity: 0.3;
        transform: scale(0.8);
    }
    30% {
        opacity: 1;
        transform: scale(1);
    }
}
